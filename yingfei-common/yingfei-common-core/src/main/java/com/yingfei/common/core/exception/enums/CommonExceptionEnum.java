package com.yingfei.common.core.exception.enums;

/**
 * 通用异常描述
 */
public enum CommonExceptionEnum implements BizExceptionEnum {

    //异常枚举
    PARAMETER_MISSING_EXCEPTION(420, "请求参数缺失"),
    PRESENCE_SUBDATA__EXCEPTION(421, "该组存在子数据"),
    DATA_NOT_FOUND_EXCEPTION(422,"该数据不存在"),
    IMPORT_DATA_NOT_NULL_EXCEPTION(423,"导入数据不能为空"),
    MQ_SEND_EXCEPTION(424,"消息发送失败"),
    DO_NOT_SUBMIT_DATA_TWICE(425,"请勿重复提交数据"),
    ACCESS_TOKEN_FAILED(426,"获取access_token失败"),
    SEND_CONFIG_NOT_EXIST(427,"消息发送配置未配置"),
    THE_IMPORTED_FILE_TYPE_IS_NOT_SUPPORTED(428,"导入文件类型不支持"),
    FEIGN_ERROR(429,"feign调用失败"),
    DATA_ALREADY_EXISTS_EXCEPTION(430,"数据已存在"),
    ;


    private final String domain = "dataManagement";
    /**
     * 异常编码
     */
    private int code;

    /**
     * 异常信息
     */
    private String message;

    CommonExceptionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }


    @Override
    public String getDomain() {
        return domain;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
