package com.yingfei.entity.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;


@Data
public class EMPL_RESPONSIBLE_INF {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long F_RESP;

    /**
     * 业务类型 0=员工与测试映射
     */
    @ApiModelProperty(value = "业务类型 0=员工与测试映射")
    private Short F_TYPE;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    private Long F_EMPL;

    /**
     * 映射业务JSON
     */
    @ApiModelProperty(value = "映射业务JSON")
    private String F_DATA;

    /**
     * 是否删除标记，默认值为0
     */
    @ApiModelProperty(value = "是否删除标记，默认值为0")
    private Integer F_DEL;

    /**
     * 记录创建用户ID
     */
    @ApiModelProperty(value = "记录创建用户ID")
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty(value = "记录编辑用户ID")
    private Long F_EDUE;

    /**
     * 记录创建日期
     */
    @ApiModelProperty(value = "记录创建日期")
    private LocalDateTime F_CRTM;

    /**
     * 记录最后编辑日期
     */
    @ApiModelProperty(value = "记录最后编辑日期")
    private LocalDateTime F_EDTM;
}
