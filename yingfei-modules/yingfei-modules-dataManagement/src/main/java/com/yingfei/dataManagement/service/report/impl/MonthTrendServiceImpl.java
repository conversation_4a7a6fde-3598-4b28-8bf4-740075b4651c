package com.yingfei.dataManagement.service.report.impl;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.dataManagement.mapper.STREAM_TREND_INFMapper;
import com.yingfei.dataManagement.service.SGRP_INFService;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.dataManagement.service.report.MonthTrendService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.STREAM_TREND_INF_DTO;
import com.yingfei.entity.dto.report.MonthTrendQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MonthTrendServiceImpl implements MonthTrendService {
    @Resource
    private STREAM_TREND_INFMapper streamTrendInfMapper;
    @Resource
    private SPEC_INFService specInfService;

    @Override
    public TableDataInfo<?> getMonthTrend(MonthTrendQueryDTO monthTrendQueryDTO) {
        if(ObjectUtils.isEmpty(monthTrendQueryDTO.getStartDate())){
            // 获取上三个月的第一天
            // 获取当前日期
            LocalDate today = LocalDate.now();
            // 获取上三个月的第一天
            Date startDate = Date.from(today.minusMonths(3).withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(today.minusMonths(1).withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            monthTrendQueryDTO.setStartDate(startDate);
            monthTrendQueryDTO.setEndDate(endDate);
        }



        // 将 Date 转换为 LocalDateTime
                LocalDateTime startDate = monthTrendQueryDTO.getStartDate()
                        .toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();

        // 获取当月第一天和最后一天
                LocalDateTime firstDay = startDate.withDayOfMonth(1).with(LocalTime.MIN);
                LocalDateTime lastDay = startDate.withDayOfMonth(startDate.getMonth().length(startDate.toLocalDate().isLeapYear()))
                        .with(LocalTime.MAX);

        // 转换回 Date（如果实体类字段类型是 Date）
                Date startOfMonth = Date.from(firstDay.atZone(ZoneId.systemDefault()).toInstant());
                Date endOfMonth = Date.from(lastDay.atZone(ZoneId.systemDefault()).toInstant());

        final Page<STREAM_TREND_INF_DTO> objectPage = BaseEntity.convertToPage(monthTrendQueryDTO.getOffset(), monthTrendQueryDTO.getNext());
//        final LambdaQueryWrapper<STREAM_TREND_INF> streamTrendInfLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        streamTrendInfLambdaQueryWrapper.ge(STREAM_TREND_INF::getF_START, startOfMonth)
//                .le(STREAM_TREND_INF::getF_START, endOfMonth);
//        streamTrendInfLambdaQueryWrapper.eq(STREAM_TREND_INF::getF_START, monthTrendQueryDTO.getStartDate());
//        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getPartList())){
//            streamTrendInfLambdaQueryWrapper.in(STREAM_TREND_INF::getF_PART, monthTrendQueryDTO.getPartList());
//        }
//        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getPtrvList())){
//            streamTrendInfLambdaQueryWrapper.in(STREAM_TREND_INF::getF_PTRV, monthTrendQueryDTO.getPtrvList());
//        }
//        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getPrcsList())){
//            streamTrendInfLambdaQueryWrapper.in(STREAM_TREND_INF::getF_PRCS, monthTrendQueryDTO.getPrcsList());
//        }
//        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getTestList())){
//            streamTrendInfLambdaQueryWrapper.in(STREAM_TREND_INF::getF_TEST, monthTrendQueryDTO.getTestList());
//        }
//        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getEmplList())){
//            streamTrendInfLambdaQueryWrapper.in(STREAM_TREND_INF::getF_EMPL, monthTrendQueryDTO.getEmplList());
//        }
//        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getSpecList())){
//            streamTrendInfLambdaQueryWrapper.in(STREAM_TREND_INF::getF_SPEC, monthTrendQueryDTO.getSpecList());
//        }
        MPJLambdaWrapper<STREAM_TREND_INF> mpjBaseMapper = new MPJLambdaWrapper<>();
        mpjBaseMapper.leftJoin(PART_INF.class, PART_INF::getF_PART, STREAM_TREND_INF::getF_PART);
        mpjBaseMapper.leftJoin(PART_REV.class, PART_REV::getF_PTRV, STREAM_TREND_INF::getF_PTRV);
        mpjBaseMapper.leftJoin(PRCS_INF.class, PRCS_INF::getF_PRCS, STREAM_TREND_INF::getF_PRCS);
        mpjBaseMapper.leftJoin(TEST_INF.class, TEST_INF::getF_TEST, STREAM_TREND_INF::getF_TEST);
        mpjBaseMapper.leftJoin(TEST_INF.class, TEST_INF::getF_TEST, STREAM_TREND_INF::getF_TEST);
        mpjBaseMapper.selectAll(STREAM_TREND_INF.class)
                .selectAs(PART_INF::getF_NAME, STREAM_TREND_INF_DTO::getPartName)
                .selectAs(PART_REV::getF_NAME, STREAM_TREND_INF_DTO::getPtrvName)
                .selectAs(PRCS_INF::getF_NAME, STREAM_TREND_INF_DTO::getPrcsName)
                .selectAs(TEST_INF::getF_NAME, STREAM_TREND_INF_DTO::getTestName);
        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getPartList())){
            mpjBaseMapper.in(STREAM_TREND_INF::getF_PART, monthTrendQueryDTO.getPartList());
        }
        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getPtrvList())){
            mpjBaseMapper.in(STREAM_TREND_INF::getF_PTRV, monthTrendQueryDTO.getPtrvList());
        }
        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getPrcsList())){
            mpjBaseMapper.in(STREAM_TREND_INF::getF_PRCS, monthTrendQueryDTO.getPrcsList());
        }
        if(ObjectUtils.isNotEmpty(monthTrendQueryDTO.getTestList())){
            mpjBaseMapper.in(STREAM_TREND_INF::getF_TEST, monthTrendQueryDTO.getTestList());
        }

        mpjBaseMapper.ge(STREAM_TREND_INF::getF_START, startOfMonth);
        mpjBaseMapper.le(STREAM_TREND_INF::getF_START, endOfMonth);
        mpjBaseMapper.orderByAsc(STREAM_TREND_INF::getF_CRTM);


//         streamTrendInfMapper.selectJoinList(STREAM_TREND_INF_DTO.class, mpjBaseMapper);
        final Page<STREAM_TREND_INF_DTO> streamTrendInfDtoPage = streamTrendInfMapper.selectJoinPage(objectPage, STREAM_TREND_INF_DTO.class, mpjBaseMapper);

        final List<STREAM_TREND_INF_DTO> records = streamTrendInfDtoPage.getRecords();


        return null;
    }

}
