package com.yingfei.system.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.yingfei.entity.domain.EMPL_RESPONSIBLE_INF;
import com.yingfei.entity.dto.EMPL_RESPONSIBLE_INF_DTO;
import com.yingfei.entity.vo.EMPL_RESPONSIBLE_INF_VO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工责任信息Mapper接口
 * <AUTHOR>
 * @description 针对表【EMPL_RESPONSIBLE_INF(员工责任信息表)】的数据库操作Mapper
 */
@Mapper
public interface EMPL_RESPONSIBLE_INFMapper extends MPJBaseMapper<EMPL_RESPONSIBLE_INF> {

    /**
     * 获取总数
     * @param emplResponsibleInfVo 查询条件
     * @return 总数
     */
    long getTotal(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo);

    /**
     * 获取列表
     * @param emplResponsibleInfVo 查询条件
     * @return 列表数据
     */
    List<EMPL_RESPONSIBLE_INF_DTO> getList(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo);

    /**
     * 根据员工ID或测试ID获取列表
     * @param emplResponsibleInfVo 查询条件
     * @return 列表数据
     */
    List<EMPL_RESPONSIBLE_INF_DTO> getListByFEmplOrFTest(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo);

    /**
     * 根据员工ID获取责任信息
     * @param fEmpl 员工ID
     * @return 责任信息列表
     */
    List<EMPL_RESPONSIBLE_INF_DTO> getByFEmpl(@Param("fEmpl") Long fEmpl);

    /**
     * 根据测试ID获取负责员工信息
     * @param fTest 测试ID
     * @return 员工责任信息列表
     */
    List<EMPL_RESPONSIBLE_INF_DTO> getByFTest(@Param("fTest") Long fTest);

    /**
     * 检查唯一性
     * @param emplResponsibleInfVo 检查条件
     * @return 数量
     */
    int checkUnique(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo);
}
