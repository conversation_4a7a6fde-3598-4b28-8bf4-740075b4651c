package com.yingfei.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.service.BaseService;
import com.yingfei.entity.domain.EMPL_RESPONSIBLE_INF;
import com.yingfei.entity.dto.EMPL_RESPONSIBLE_INF_DTO;
import com.yingfei.entity.vo.EMPL_RESPONSIBLE_INF_VO;

import java.util.List;

/**
 * 员工责任信息Service接口
 * <AUTHOR>
 * @description 针对表【EMPL_RESPONSIBLE_INF(员工责任信息表)】的数据库操作Service
 */
public interface EMPL_RESPONSIBLE_INFService extends IService<EMPL_RESPONSIBLE_INF>, BaseService<EMPL_RESPONSIBLE_INF_VO, EMPL_RESPONSIBLE_INF_DTO> {

    /**
     * 编辑员工责任信息
     * @param emplResponsibleInfVo 员工责任信息
     */
    void edit(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo);

    /**
     * 根据员工ID或测试ID获取列表
     * @param emplResponsibleInfVo 查询条件
     * @return 列表数据
     */
    List<EMPL_RESPONSIBLE_INF_DTO> getListByFEmplOrFTest(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo);

    /**
     * 根据员工ID获取责任信息
     * @param fEmpl 员工ID
     * @return 责任信息列表
     */
    List<EMPL_RESPONSIBLE_INF_DTO> getByFEmpl(Long fEmpl);

    /**
     * 根据测试ID获取负责员工信息
     * @param fTest 测试ID
     * @return 员工责任信息列表
     */
    List<EMPL_RESPONSIBLE_INF_DTO> getByFTest(Long fTest);

    /**
     * 批量添加员工责任信息
     * @param emplResponsibleInfVoList 员工责任信息列表
     */
    void batchAdd(List<EMPL_RESPONSIBLE_INF_VO> emplResponsibleInfVoList);

    /**
     * 批量编辑员工责任信息
     * @param emplResponsibleInfVoList 员工责任信息列表
     */
    void batchEdit(List<EMPL_RESPONSIBLE_INF_VO> emplResponsibleInfVoList);

    /**
     * 根据员工ID删除责任信息
     * @param fEmpl 员工ID
     */
    void deleteByFEmpl(Long fEmpl);

    /**
     * 根据测试ID删除责任信息
     * @param fTest 测试ID
     */
    void deleteByFTest(Long fTest);
}
