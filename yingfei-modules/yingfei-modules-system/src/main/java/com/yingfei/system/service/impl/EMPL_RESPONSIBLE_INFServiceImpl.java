package com.yingfei.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.entity.domain.EMPL_RESPONSIBLE_INF;
import com.yingfei.entity.dto.EMPL_RESPONSIBLE_INF_DTO;
import com.yingfei.entity.vo.EMPL_RESPONSIBLE_INF_VO;
import com.yingfei.system.mapper.EMPL_RESPONSIBLE_INFMapper;
import com.yingfei.system.service.EMPL_RESPONSIBLE_INFService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 员工责任信息Service实现类
 * <AUTHOR>
 * @description 针对表【EMPL_RESPONSIBLE_INF(员工责任信息表)】的数据库操作Service实现
 */
@Slf4j
@Service
public class EMPL_RESPONSIBLE_INFServiceImpl extends ServiceImpl<EMPL_RESPONSIBLE_INFMapper, EMPL_RESPONSIBLE_INF>
        implements EMPL_RESPONSIBLE_INFService {

    @Override
    public long getTotal(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        return baseMapper.getTotal(emplResponsibleInfVo);
    }

    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getList(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        return baseMapper.getList(emplResponsibleInfVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        // 参数校验
        checkParam(emplResponsibleInfVo);
        
        EMPL_RESPONSIBLE_INF emplResponsibleInf = new EMPL_RESPONSIBLE_INF();
        BeanUtils.copyPropertiesIgnoreNull(emplResponsibleInfVo, emplResponsibleInf);
        
        // 设置默认值
        emplResponsibleInf.setF_RESP(JudgeUtils.defaultIdentifierGenerator.nextId(null));
        emplResponsibleInf.setF_DEL(DelFlagEnum.USE.getType());
        emplResponsibleInf.setF_CRUE(SecurityUtils.getUserId());
        emplResponsibleInf.setF_CRTM(LocalDateTime.now());
        
        baseMapper.insert(emplResponsibleInf);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        if (ObjectUtils.isEmpty(emplResponsibleInfVo.getF_RESP())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        
        // 参数校验
        checkParam(emplResponsibleInfVo);
        
        EMPL_RESPONSIBLE_INF emplResponsibleInf = new EMPL_RESPONSIBLE_INF();
        BeanUtils.copyPropertiesIgnoreNull(emplResponsibleInfVo, emplResponsibleInf);
        
        // 设置编辑信息
        emplResponsibleInf.setF_EDUE(SecurityUtils.getUserId());
        emplResponsibleInf.setF_EDTM(LocalDateTime.now());
        
        baseMapper.updateById(emplResponsibleInf);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        
        LambdaUpdateWrapper<EMPL_RESPONSIBLE_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(EMPL_RESPONSIBLE_INF::getF_RESP, ids);
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.DELETE.getType());
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_EDUE, SecurityUtils.getUserId());
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_EDTM, LocalDateTime.now());
        
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public void checkParam(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        if (ObjectUtils.isEmpty(emplResponsibleInfVo.getF_TYPE())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        if (ObjectUtils.isEmpty(emplResponsibleInfVo.getF_EMPL())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        if (StringUtils.isBlank(emplResponsibleInfVo.getF_DATA())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        
        // 检查唯一性（同一员工同一业务类型只能有一条记录）
        int count = baseMapper.checkUnique(emplResponsibleInfVo);
        if (count > 0) {
            throw new BusinessException(CommonExceptionEnum.DATA_ALREADY_EXISTS_EXCEPTION);
        }
    }

    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getListByFEmplOrFTest(EMPL_RESPONSIBLE_INF_VO emplResponsibleInfVo) {
        return baseMapper.getListByFEmplOrFTest(emplResponsibleInfVo);
    }

    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getByFEmpl(Long fEmpl) {
        if (ObjectUtils.isEmpty(fEmpl)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        return baseMapper.getByFEmpl(fEmpl);
    }

    @Override
    public List<EMPL_RESPONSIBLE_INF_DTO> getByFTest(Long fTest) {
        if (ObjectUtils.isEmpty(fTest)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        return baseMapper.getByFTest(fTest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<EMPL_RESPONSIBLE_INF_VO> emplResponsibleInfVoList) {
        if (CollectionUtils.isEmpty(emplResponsibleInfVoList)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        
        for (EMPL_RESPONSIBLE_INF_VO vo : emplResponsibleInfVoList) {
            add(vo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchEdit(List<EMPL_RESPONSIBLE_INF_VO> emplResponsibleInfVoList) {
        if (CollectionUtils.isEmpty(emplResponsibleInfVoList)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        
        for (EMPL_RESPONSIBLE_INF_VO vo : emplResponsibleInfVoList) {
            edit(vo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByFEmpl(Long fEmpl) {
        if (ObjectUtils.isEmpty(fEmpl)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        
        LambdaUpdateWrapper<EMPL_RESPONSIBLE_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(EMPL_RESPONSIBLE_INF::getF_EMPL, fEmpl);
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.DELETE.getType());
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_EDUE, SecurityUtils.getUserId());
        updateWrapper.set(EMPL_RESPONSIBLE_INF::getF_EDTM, LocalDateTime.now());
        
        baseMapper.update(null, updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByFTest(Long fTest) {
        if (ObjectUtils.isEmpty(fTest)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        
        LambdaQueryWrapper<EMPL_RESPONSIBLE_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EMPL_RESPONSIBLE_INF::getF_DEL, DelFlagEnum.USE.getType());
        queryWrapper.like(EMPL_RESPONSIBLE_INF::getF_DATA, "\"" + fTest + "\"");
        
        List<EMPL_RESPONSIBLE_INF> list = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> ids = list.stream().map(EMPL_RESPONSIBLE_INF::getF_RESP).toList();
            del(ids);
        }
    }
}
