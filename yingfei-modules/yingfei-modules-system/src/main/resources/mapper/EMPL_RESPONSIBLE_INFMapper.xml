<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.EMPL_RESPONSIBLE_INFMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.EMPL_RESPONSIBLE_INF">
        <id column="F_RESP" property="F_RESP"/>
        <result column="F_TYPE" property="F_TYPE"/>
        <result column="F_EMPL" property="F_EMPL"/>
        <result column="F_DATA" property="F_DATA"/>
        <result column="F_DEL" property="F_DEL"/>
        <result column="F_CRUE" property="F_CRUE"/>
        <result column="F_EDUE" property="F_EDUE"/>
        <result column="F_CRTM" property="F_CRTM"/>
        <result column="F_EDTM" property="F_EDTM"/>
    </resultMap>

    <!-- 查询结果映射 -->
    <resultMap id="DTOResultMap" type="com.yingfei.entity.dto.EMPL_RESPONSIBLE_INF_DTO">
        <id column="F_RESP" property="F_RESP"/>
        <result column="F_TYPE" property="F_TYPE"/>
        <result column="F_EMPL" property="F_EMPL"/>
        <result column="F_DATA" property="F_DATA"/>
        <result column="F_DEL" property="F_DEL"/>
        <result column="F_CRUE" property="F_CRUE"/>
        <result column="F_EDUE" property="F_EDUE"/>
        <result column="F_CRTM" property="F_CRTM"/>
        <result column="F_EDTM" property="F_EDTM"/>
        <result column="emplName" property="emplName"/>
        <result column="emplWorkId" property="emplWorkId"/>
        <result column="emplEmail" property="emplEmail"/>
        <result column="createUserName" property="createUserName"/>
        <result column="editUserName" property="editUserName"/>
        <result column="typeName" property="typeName"/>
    </resultMap>

    <!-- 通用查询条件 -->
    <sql id="baseSql">
        from EMPL_RESPONSIBLE_INF eri
        left join EMPL_INF ei on ei.F_EMPL = eri.F_EMPL
        left join EMPL_INF ei_create on ei_create.F_EMPL = eri.F_CRUE
        left join EMPL_INF ei_edit on ei_edit.F_EMPL = eri.F_EDUE
        <where>
            1=1
            <choose>
                <when test="F_DEL == null">
                    and eri.F_DEL = 0
                </when>
                <otherwise>
                    and eri.F_DEL = #{F_DEL}
                </otherwise>
            </choose>
            <if test="F_RESP != null">
                and eri.F_RESP = #{F_RESP}
            </if>
            <if test="F_TYPE != null">
                and eri.F_TYPE = #{F_TYPE}
            </if>
            <if test="F_EMPL != null">
                and eri.F_EMPL = #{F_EMPL}
            </if>
            <if test="emplIds != null and emplIds.size() > 0">
                and eri.F_EMPL in
                <foreach collection="emplIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="F_TEST != null">
                and eri.F_DATA like concat('%"', #{F_TEST}, '"%')
            </if>
            <if test="testIds != null and testIds.size() > 0">
                and (
                <foreach collection="testIds" item="item" separator=" or ">
                    eri.F_DATA like concat('%"', #{item}, '"%')
                </foreach>
                )
            </if>
            <if test="F_DATA != null and F_DATA != ''">
                and eri.F_DATA like concat('%', #{F_DATA}, '%')
            </if>
        </where>
    </sql>

    <!-- 获取总数 -->
    <select id="getTotal" resultType="long">
        select count(1)
        <include refid="baseSql"/>
    </select>

    <!-- 获取列表 -->
    <select id="getList" resultMap="DTOResultMap">
        select eri.F_RESP,
               eri.F_TYPE,
               eri.F_EMPL,
               eri.F_DATA,
               eri.F_DEL,
               eri.F_CRUE,
               eri.F_EDUE,
               eri.F_CRTM,
               eri.F_EDTM,
               ei.F_NAME as emplName,
               ei.F_WORKID as emplWorkId,
               ei.F_EMAIL as emplEmail,
               ei_create.F_NAME as createUserName,
               ei_edit.F_NAME as editUserName,
               case eri.F_TYPE
                   when 0 then '员工与测试映射'
                   else '未知类型'
               end as typeName
        <include refid="baseSql"/>
        order by eri.F_CRTM desc
    </select>

    <!-- 根据员工ID或测试ID获取列表 -->
    <select id="getListByFEmplOrFTest" resultMap="DTOResultMap">
        select eri.F_RESP,
               eri.F_TYPE,
               eri.F_EMPL,
               eri.F_DATA,
               eri.F_DEL,
               eri.F_CRUE,
               eri.F_EDUE,
               eri.F_CRTM,
               eri.F_EDTM,
               ei.F_NAME as emplName,
               ei.F_WORKID as emplWorkId,
               ei.F_EMAIL as emplEmail,
               ei_create.F_NAME as createUserName,
               ei_edit.F_NAME as editUserName,
               case eri.F_TYPE
                   when 0 then '员工与测试映射'
                   else '未知类型'
               end as typeName
        <include refid="baseSql"/>
        order by eri.F_CRTM desc
    </select>

    <!-- 根据员工ID获取责任信息 -->
    <select id="getByFEmpl" resultMap="DTOResultMap">
        select eri.F_RESP,
               eri.F_TYPE,
               eri.F_EMPL,
               eri.F_DATA,
               eri.F_DEL,
               eri.F_CRUE,
               eri.F_EDUE,
               eri.F_CRTM,
               eri.F_EDTM,
               ei.F_NAME as emplName,
               ei.F_WORKID as emplWorkId,
               ei.F_EMAIL as emplEmail,
               ei_create.F_NAME as createUserName,
               ei_edit.F_NAME as editUserName,
               case eri.F_TYPE
                   when 0 then '员工与测试映射'
                   else '未知类型'
               end as typeName
        from EMPL_RESPONSIBLE_INF eri
        left join EMPL_INF ei on ei.F_EMPL = eri.F_EMPL
        left join EMPL_INF ei_create on ei_create.F_EMPL = eri.F_CRUE
        left join EMPL_INF ei_edit on ei_edit.F_EMPL = eri.F_EDUE
        where eri.F_DEL = 0
          and eri.F_EMPL = #{fEmpl}
        order by eri.F_CRTM desc
    </select>

    <!-- 根据测试ID获取负责员工信息 -->
    <select id="getByFTest" resultMap="DTOResultMap">
        select eri.F_RESP,
               eri.F_TYPE,
               eri.F_EMPL,
               eri.F_DATA,
               eri.F_DEL,
               eri.F_CRUE,
               eri.F_EDUE,
               eri.F_CRTM,
               eri.F_EDTM,
               ei.F_NAME as emplName,
               ei.F_WORKID as emplWorkId,
               ei.F_EMAIL as emplEmail,
               ei_create.F_NAME as createUserName,
               ei_edit.F_NAME as editUserName,
               case eri.F_TYPE
                   when 0 then '员工与测试映射'
                   else '未知类型'
               end as typeName
        from EMPL_RESPONSIBLE_INF eri
        left join EMPL_INF ei on ei.F_EMPL = eri.F_EMPL
        left join EMPL_INF ei_create on ei_create.F_EMPL = eri.F_CRUE
        left join EMPL_INF ei_edit on ei_edit.F_EMPL = eri.F_EDUE
        where eri.F_DEL = 0
          and eri.F_DATA like concat('%"', #{fTest}, '"%')
        order by eri.F_CRTM desc
    </select>

    <!-- 检查唯一性 -->
    <select id="checkUnique" resultType="int">
        select count(1)
        from EMPL_RESPONSIBLE_INF
        where F_DEL = 0
          and F_EMPL = #{F_EMPL}
          and F_TYPE = #{F_TYPE}
        <if test="F_RESP != null">
            and F_RESP != #{F_RESP}
        </if>
    </select>

</mapper>
